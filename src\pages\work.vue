<template>
  <view class="video-edit-container">
    <!-- 顶部标题区域 -->
    <view class="page-header">
      <view class="header-content">
        <view class="title-section">
          <view class="title-icon">
            <text class="iconfont icon-video"></text>
          </view>
          <view class="title-text">
            <text class="main-title">云剪辑工作台</text>
            <text class="sub-title">智能视频编辑，让创作更简单</text>
          </view>
        </view>
        <view class="header-actions">
          <up-button
            type="primary"
            size="normal"
            @click="handleCreate"
            class="create-btn"
          >
            <text class="btn-icon">➕</text>
            <text>创建新工程</text>
          </up-button>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <SimpleSearchFilter
      v-model:activeFilter="queryParams.status"
      v-model:startTime="queryParams.startTime"
      v-model:endTime="queryParams.endTime"
      :filter-tags="statusOptions"
      :show-result-count="true"
      :result-count="taskList.length"
      :show-time-filter="true"
      @search="handleSearch"
      @clear-all="handleClearAll"
    />

    <!-- 项目列表组件 -->
    <view class="project-list-wrapper">
      <ProjectList
        :project-list="taskList"
        :loading="loading"
        @item-click="handleItemClick"
        @edit="handleEdit"
        @delete="handleDelete"
        @create="handleCreate"
      />
    </view>

    <up-loadmore
      :status="status"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      dashed
      line
      v-if="taskList.length > 0 && !loading"
    />

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteDialog"
      :title="deleteDialogTitle"
      headerText="删除确认"
      :showIcon="true"
      icon="🗑️"
      iconColor="#ff4757"
      confirmText="删除"
      cancelText="取消"
      color="#ff4757"
      warningText="删除后无法恢复"
      @confirm="handleConfirmDelete"
      @cancel="handleCancelDelete"
    />
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import {
  listEditingProjects,
  listEditingProjectsDB,
  deleteEditingProjects,
} from "@/api/platform/videoEdit.js";
import { onReachBottom, onPullDownRefresh } from "@dcloudio/uni-app";
import ProjectList from "@/components/platform/project-list.vue";
import SimpleSearchFilter from "@/components/platform/simple-search-filter.vue";
import ConfirmDialog from "@/components/platform/confirm-dialog.vue";
import modal from "@/plugins/modal";

const status = ref("loadmore");
const loadmoreText = ref("轻轻上拉");
const loadingText = ref("努力加载中");
const nomoreText = ref("实在没有了");

// 响应式数据
const loading = ref(false);
const taskList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: "",
  status: "",
  startTime: "",
  endTime: "",
});

//对话框的数据
const showDeleteDialog = ref(false);
const pendingDeleteItem = ref(null);
const deleteDialogTitle = computed(() => {
  if (!pendingDeleteItem.value) return "确定要删除该工程吗？";
});

const total = ref(null);

// 页面加载时获取数据
onMounted(() => {
  getTaskList();
  // 监听来自视频编辑页面的刷新事件
  uni.$on("refreshWorkList", () => {
    getTaskList();
  });
});

// 获取项目列表
const getTaskList = async () => {
  try {
    status.value = "loading";
    const res = await listEditingProjectsDB(queryParams.value);
    console.log("获取到的列表数据:", res);
    total.value = res.total;
    if (queryParams.value.pageNum == 1) {
      taskList.value = res.rows || [];
    } else {
      taskList.value.push(...(res.rows || []));
    }
    status.value = "loadmore";
    uni.stopPullDownRefresh();

  } catch (error) {
    modal.msg(`加载失败${error}`);
  } finally {
    status.value = "loadmore";
    uni.stopPullDownRefresh();
  }
};

/**下拉刷新 */
onPullDownRefresh(() => {
    taskList.value = [];
    queryParams.value.pageNum = 1;
    getTaskList(queryParams.value);
});

/** 上拉加载更多 */
onReachBottom(() => {
    if (taskList.value.length >= total.value) {
        status.value = 'nomore';
        return;
    }
    queryParams.value.pageNum += 1;
    getTaskList();
});

// 状态选项
const statusOptions = computed(() => [
  { label: "全部", value: "" },
  { label: "草稿", value: "Draft" },
  { label: "编辑中", value: "Editing" },
  { label: "制作中", value: "Producing" },
  { label: "已完成", value: "Produced" },
  { label: "失败", value: "ProduceFailed" },
]);

// 搜索处理函数
const handleSearch = (searchData) => {
  console.log("搜索数据:", searchData);

  // 将搜索数据赋值给查询参数
  if (searchData) {
    queryParams.value.projectId = searchData.projectId || "";
    queryParams.value.status = searchData.filter || "";
    queryParams.value.startTime = searchData.startTime || "";
    queryParams.value.endTime = searchData.endTime || "";
  }

  console.log("更新后的查询参数:", queryParams.value);

  // 触发列表刷新
  getTaskList(true);
};

// 清空所有筛选
const handleClearAll = () => {
  console.log("清空所有筛选");
  queryParams.value.projectId = "";
  queryParams.value.status = "";
  queryParams.value.startTime = "";
  queryParams.value.endTime = "";
  // 触发列表刷新
  getTaskList(true);
};

// 项目列表项点击事件
function handleItemClick(item) {
  console.log("点击项目:", item);
}

// 创建工程 - 跳转到视频剪辑页面
function handleCreate() {
  uni.navigateTo({
    url: "/pages_workbench/pages/videoEdit/index?from=work",
    success: (res) => {
      console.log("跳转成功:", res);
    },
    fail: (err) => {
      modal.msg(`跳转失败: ${err.errMsg}`);
    },
  });
}

// 编辑工程 - 跳转到视频剪辑页面
function handleEdit(item) {
  uni.navigateTo({
    url: `/pages/index?from=work&projectId=${
      item.ProjectId
    }&title=${encodeURIComponent(item.Title)}`,
  });
}

/**删除方法 */
function handleDelete(item) {
  pendingDeleteItem.value = item;
  showDeleteDialog.value = true;
}

/**确认删除方法 */
async function handleConfirmDelete() {
  if (!pendingDeleteItem.value) return;

  try {
    uni.showLoading({
      title: "删除中...",
    });

    await deleteEditingProjects([pendingDeleteItem.value.ProjectId]);

    uni.hideLoading();

    modal.msg("删除成功");

    getTaskList(true);
  } catch (error) {
    uni.hideLoading();
    console.error("删除失败:", error);
    uni.showToast({
      title: "删除失败",
      icon: "error",
    });
  } finally {
    // 清理状态
    pendingDeleteItem.value = null;
    showDeleteDialog.value = false;
  }
}

function handleCancelDelete() {
  pendingDeleteItem.value = null;
  showDeleteDialog.value = false;
}

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off("refreshWorkList");
});
</script>

<style lang="scss" scoped>
.video-edit-container {
  min-height: 100vh;
  background-color: #f5f5f5;

  // 顶部标题区域
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20rpx 30rpx;
    padding-top: calc(var(--status-bar-height) + 20rpx);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        display: flex;
        align-items: center;

        .title-icon {
          width: 60rpx;
          height: 60rpx;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;

          .iconfont {
            font-size: 32rpx;
            color: #fff;

            &.icon-video:before {
              content: "🎬";
            }
          }
        }

        .title-text {
          display: flex;
          flex-direction: column;

          .main-title {
            font-size: 36rpx;
            font-weight: bold;
            color: #fff;
            line-height: 1.2;
          }

          .sub-title {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 4rpx;
          }
        }
      }

      .header-actions {
        .create-btn {
          background: rgba(255, 255, 255, 0.2) !important;
          border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
          border-radius: 50rpx !important;
          padding: 16rpx 24rpx !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          .btn-icon {
            font-size: 24rpx;
            color: #fff;
            margin-right: 8rpx;
          }

          text {
            color: #fff;
            font-size: 28rpx;
          }
        }
      }
    }
  }

  // 项目列表包装器
  .project-list-wrapper {
    padding: 20rpx;
  }
}
</style>
