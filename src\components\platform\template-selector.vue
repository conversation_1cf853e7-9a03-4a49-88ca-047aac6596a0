<template>
  <view class="template-selector">
    <!-- 触发按钮 -->
    <NavItem
      title="选择模版"
      :value="selectedTemplate ? selectedTemplate.Name : ''"
      :placeholder="placeholder"
      @click="openTemplateSelector"
    />

    <!-- ActionSheet 弹出菜单 -->
    <u-action-sheet
      :show="showActionSheet"
      @close="closeActionSheet"
      cancelText="取消"
      round="true"
      :safeAreaInsetBottom="true"
      closeOnClickOverlay="true"
    >
      <view class="action-sheet-container">
        <!-- 标题区域 -->
        <view class="action-sheet-header">
          <view class="header-content">
            <text class="header-title">选择模板</text>
            <text class="header-subtitle">共 {{ templates.length }} 个模板</text>
          </view>
          <view class="header-divider"></view>
        </view>

        <!-- 内容区域 -->
        <view class="custom-content" v-if="templates.length > 0">
          <scroll-view class="template-scroll" scroll-y="true" :show-scrollbar="false">
            <view class="template-list">
              <view
                class="template-item"
                v-for="(template, index) in templates"
                :key="template.TemplateId"
                @click="selectTemplate(template, index)"
                :class="{ 'selected': selectedTemplate && selectedTemplate.TemplateId === template.TemplateId }"
                :style="{ 'animation-delay': (index * 0.1) + 's' }"
              >
                <view class="template-image-container">
                  <image
                    class="template-image"
                    :src="template.imageAddress"
                    mode="aspectFill"
                  />
                  <view class="image-overlay" v-if="selectedTemplate && selectedTemplate.TemplateId === template.TemplateId">
                    <view class="check-icon-container">
                      <text class="check-icon">✓</text>
                    </view>
                  </view>
                </view>
                <view class="template-info">
                  <view class="info-header">
                    <text class="template-name">{{ template.Name }}</text>
                    <view class="template-desc-container">
                      <text class="template-desc">{{getStatus(template.Status)}}</text>
                    </view>
                  </view>
                  <view class="template-meta">
                    <view class="meta-item">
                      <text class="meta-label">来源:</text>
                      <text class="template-source">{{ template.CreateSource }}</text>
                    </view>
                    <view class="meta-item">
                      <text class="meta-label">时间:</text>
                      <text class="template-time">{{ formatTime(template.CreationTime) }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view v-else class="empty-template">
          <view class="empty-icon">📋</view>
          <text class="empty-text">暂无模板数据</text>
        </view>
      </view>
    </u-action-sheet>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import NavItem from "@/components/platform/nav-item.vue";
import { listTemplates } from "@/api/platform/template";

// 定义属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => null,
  },
  placeholder: {
    type: String,
    default: "点击选择模版",
  },
});

// 定义事件
const emit = defineEmits(["update:modelValue"]);

// 数据状态
const showActionSheet = ref(false);
const templates = ref([]);
const selectedTemplate = computed(() => props.modelValue);

// 获取模板列表
const getTemplateList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 999,
    };
    const res = await listTemplates(params);
    console.log("res", res);
    if (res.code === 200) {
      templates.value = res.data.Templates || [];
    }
    console.log("templates", templates);
  } catch (err) {
    console.error("获取模板列表失败:", err);
  }
};

// 打开模板选择器
const openTemplateSelector = () => {
  getTemplateList();
  showActionSheet.value = true;
};

// 关闭模板选择器
const closeActionSheet = () => {
  showActionSheet.value = false;
};

// 直接选择模板（通过点击列表项）
const selectTemplate = (template) => {
  emit("update:modelValue", template);
  closeActionSheet();
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  try {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    if (days < 30) return `${Math.floor(days / 7)}周前`;
    if (days < 365) return `${Math.floor(days / 30)}个月前`;
    return `${Math.floor(days / 365)}年前`;
  } catch (e) {
    return timeStr;
  }
};

// 获取状态标签
const getStatus = (status) => {
  const statusMap = {
    Available: { label: '正常', type: 'success', effect: 'light' },
    UploadFailed: { label: '上传失败', type: 'danger', effect: 'light' },
    ProcessFailed: { label: '高级模版分析失败', type: 'danger', effect: 'light' },
    Uploading: { label: '上传中', type: 'warning', effect: 'light' },
    Created: { label: '已创建，还不能使用', type: 'info', effect: 'light' },
    Processing: { label: '高级模板分析中', type: 'warning', effect: 'light' }
  };

  return statusMap[status]?.label || status || '未知状态';
};



// 组件挂载时获取模板列表
onMounted(() => {
  getTemplateList();
});
</script>

<style lang="scss" scoped>
.template-selector {
  .action-sheet-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;

    /*  #ifdef  MP-WEIXIN  */
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
    /*  #endif  */
  }

  .action-sheet-header {
    padding: 32rpx 32rpx 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);

    /*  #ifdef  MP-WEIXIN  */
    background: rgba(42, 42, 42, 0.95);
    /*  #endif  */

    .header-content {
      text-align: center;
      margin-bottom: 24rpx;

      .header-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8rpx;

        /*  #ifdef  MP-WEIXIN  */
        color: #ffffff;
        /*  #endif  */
      }

      .header-subtitle {
        display: block;
        font-size: 24rpx;
        color: #666;

        /*  #ifdef  MP-WEIXIN  */
        color: #aaa;
        /*  #endif  */
      }
    }

    .header-divider {
      height: 4rpx;
      background: linear-gradient(90deg, transparent 0%, #007aff 50%, transparent 100%);
      border-radius: 2rpx;
      margin: 0 auto;
      width: 120rpx;
    }
  }

  .custom-content {
    padding: 24rpx 0 0;
    max-height: 65vh;

    .template-scroll {
      max-height: 60vh;

      .template-list {
        padding: 0 24rpx 32rpx;
        display: flex;
        flex-direction: column;
        gap: 12rpx;

        .template-item {
          position: relative;
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 24rpx;
          border-radius: 24rpx;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20rpx);
          border: 2rpx solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
          transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
          overflow: hidden;
          min-height: 160rpx;
          opacity: 0;
          transform: translateY(30rpx);
          animation: slideInUp 0.6s ease-out forwards;

          /*  #ifdef  MP-WEIXIN  */
          background: rgba(42, 42, 42, 0.95);
          border: 2rpx solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
          /*  #endif  */

          &:hover {
            transform: translateY(-4rpx) scale(1.02);
            box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
            border-color: rgba(0, 122, 255, 0.3);

            /*  #ifdef  MP-WEIXIN  */
            box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.4);
            /*  #endif  */

            .item-arrow {
              transform: translateX(8rpx);
              opacity: 1;
            }

            .template-image {
              transform: scale(1.05);
            }
          }

          &:active {
            transform: translateY(-2rpx) scale(0.98);
            transition: all 0.2s ease;
          }

          &.selected {
            border-color: #007aff;
            box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.25);
            transform: translateY(-2rpx) scale(1.01);
            background: rgba(0, 122, 255, 0.05);

            /*  #ifdef  MP-WEIXIN  */
            border-color: #007aff;
            box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.4);
            background: rgba(0, 122, 255, 0.1);
            /*  #endif  */

            .item-arrow {
              color: #007aff;
              transform: translateX(8rpx);
              opacity: 1;
            }

            .template-name {
              color: #007aff;

              /*  #ifdef  MP-WEIXIN  */
              color: #4da6ff;
              /*  #endif  */
            }
          }

          .template-image-container {
            position: relative;
            width: 180rpx;
            height: 120rpx;
            margin-right: 24rpx;
            flex-shrink: 0;
            border-radius: 16rpx;
            overflow: hidden;
            box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

            /*  #ifdef  MP-WEIXIN  */
            box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
            /*  #endif  */

            .template-image {
              width: 100%;
              height: 100%;
              border-radius: 16rpx;
              background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              object-fit: cover;
              transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              /*  #ifdef  MP-WEIXIN  */
              background: linear-gradient(135deg, #495057 0%, #343a40 100%);
              /*  #endif  */
            }

            .image-loading {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(248, 249, 250, 0.9);
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              /*  #ifdef  MP-WEIXIN  */
              background: rgba(73, 80, 87, 0.9);
              /*  #endif  */

              .loading-spinner {
                width: 32rpx;
                height: 32rpx;
                border: 3rpx solid rgba(0, 122, 255, 0.2);
                border-top: 3rpx solid #007aff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
              }
            }

            .image-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(0, 122, 255, 0.9) 0%, rgba(0, 122, 255, 0.7) 100%);
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              animation: overlayFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              .check-icon-container {
                width: 60rpx;
                height: 60rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: checkBounce 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

                .check-icon {
                  font-size: 36rpx;
                  color: #ffffff;
                  font-weight: bold;
                }
              }
            }
          }

          .template-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 120rpx;
            padding-right: 16rpx;

            .info-header {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              margin-bottom: 12rpx;
              gap: 16rpx;

              .template-name {
                font-size: 32rpx;
                font-weight: 600;
                color: #1a1a1a;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                line-height: 1.3;
                text-align: left;
                transition: color 0.3s ease;
                min-width: 0; /* 确保flex子项可以收缩 */

                /*  #ifdef  MP-WEIXIN  */
                color: #ffffff;
                /*  #endif  */
              }

              .template-desc-container {
                flex-shrink: 0; /* 防止状态标签被压缩 */

                .template-desc {
                  font-size: 22rpx;
                  color: #666;
                  padding: 6rpx 12rpx;
                  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(0, 122, 255, 0.05) 100%);
                  border-radius: 12rpx;
                  font-weight: 500;
                  border: 1rpx solid rgba(0, 122, 255, 0.2);
                  transition: all 0.3s ease;
                  white-space: nowrap;

                  /*  #ifdef  MP-WEIXIN  */
                  color: #bbb;
                  background: linear-gradient(135deg, rgba(0, 122, 255, 0.2) 0%, rgba(0, 122, 255, 0.1) 100%);
                  border-color: rgba(0, 122, 255, 0.3);
                  /*  #endif  */
                }
              }
            }

            .template-meta {
              display: flex;
              flex-direction: column;
              gap: 6rpx;

              .meta-item {
                display: flex;
                align-items: center;
                font-size: 22rpx;
                line-height: 1.4;

                .meta-label {
                  color: #999;
                  margin-right: 8rpx;
                  font-weight: 500;
                  min-width: 60rpx;

                  /*  #ifdef  MP-WEIXIN  */
                  color: #888;
                  /*  #endif  */
                }

                .template-source,
                .template-time {
                  color: #666;
                  font-weight: 500;
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;

                  /*  #ifdef  MP-WEIXIN  */
                  color: #aaa;
                  /*  #endif  */
                }
              }
            }
          }

          .item-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40rpx;
            height: 40rpx;
            opacity: 0.3;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform: translateX(-8rpx);

            .arrow-icon {
              font-size: 32rpx;
              color: #666;
              font-weight: bold;

              /*  #ifdef  MP-WEIXIN  */
              color: #aaa;
              /*  #endif  */
            }
          }
        }
      }
    }
  }

  .empty-template {
    padding: 80rpx 40rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;

    .empty-icon {
      font-size: 120rpx;
      margin-bottom: 24rpx;
      opacity: 0.6;
      animation: bounce 2s infinite;
    }

    .empty-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #666;
      margin-bottom: 12rpx;

      /*  #ifdef  MP-WEIXIN  */
      color: #aaa;
      /*  #endif  */
    }

    .empty-desc {
      font-size: 24rpx;
      color: #999;
      line-height: 1.5;

      /*  #ifdef  MP-WEIXIN  */
      color: #888;
      /*  #endif  */
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes checkBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
